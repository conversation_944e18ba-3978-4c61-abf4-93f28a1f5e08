/**
 * AutoSnapshotService.ts
 * 
 * Automatically generates snapshots when Enter key is pressed in input boxes.
 * Saves snapshots as [mindbook]_[timestamp].json with maximum 3 versions.
 */

import { MemorySnapshotService } from '../core/services/MemorySnapshotService';
import { MemoryAPI } from './api/MemoryAPI';
import { useMindBookStore } from '../core/state/MindBookStore';

export interface AutoSnapshotConfig {
  enabled: boolean;
  maxVersions: number;
  debounceMs: number;
  targetInputs: string[];
}

export class AutoSnapshotService {
  private static instance: AutoSnapshotService;
  private config: AutoSnapshotConfig;
  private lastSnapshotTime: number = 0;
  private isProcessing: boolean = false;

  private constructor() {
    this.config = {
      enabled: true,
      maxVersions: 3,
      debounceMs: 2000, // 2 seconds debounce
      targetInputs: [
        'governance-input',     // Governance box
        'chatfork-input',      // ChatFork input
        'node-input',          // Node input boxes
        'mindsheet-input'      // Any mindsheet input
      ]
    };

    this.initializeEventListeners();
  }

  static getInstance(): AutoSnapshotService {
    if (!this.instance) {
      this.instance = new AutoSnapshotService();
    }
    return this.instance;
  }

  /**
   * Initialize global event listeners for Enter key detection
   */
  private initializeEventListeners(): void {
    // Listen for keydown events globally
    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);

    console.log('🔄 AutoSnapshotService: Event listeners initialized');
    console.log('🔄 AutoSnapshotService: Listening for Enter key on target inputs:', this.config.targetInputs);

    // Add a test to verify event listener is working
    setTimeout(() => {
      console.log('🔄 AutoSnapshotService: Testing event listener after 3 seconds...');
      const testEvent = new KeyboardEvent('keydown', { key: 'Enter', bubbles: true });
      console.log('🔄 AutoSnapshotService: Event listener test completed');
    }, 3000);
  }

  /**
   * Handle keydown events and detect Enter key in target inputs
   */
  private async handleKeyDown(event: KeyboardEvent): Promise<void> {
    // Only process Enter key
    if (event.key !== 'Enter') {
      return;
    }

    // Check if we're in a target input
    const target = event.target as HTMLElement;
    console.log('AutoSnapshotService: Enter key detected on element:', {
      tagName: target.tagName,
      id: target.id,
      className: target.className,
      isTargetInput: this.isTargetInput(target)
    });

    if (!this.isTargetInput(target)) {
      console.log('AutoSnapshotService: Not a target input, skipping');
      return;
    }

    // Check if snapshots are enabled
    if (!this.config.enabled) {
      console.log('AutoSnapshotService: Snapshots disabled, skipping');
      return;
    }

    // Debounce to prevent rapid snapshots
    const now = Date.now();
    if (now - this.lastSnapshotTime < this.config.debounceMs) {
      console.log('AutoSnapshotService: Debounced, skipping snapshot');
      return;
    }

    // Check if we're already processing
    if (this.isProcessing) {
      console.log('AutoSnapshotService: Already processing, skipping snapshot');
      return;
    }

    // Check if we have active content to snapshot
    if (!this.hasActiveContent()) {
      console.log('AutoSnapshotService: No active content, skipping snapshot');
      return;
    }

    console.log('AutoSnapshotService: All checks passed, generating snapshot...');
    // Generate snapshot
    await this.generateSnapshot(target);
  }

  /**
   * Check if the target element is one we should monitor
   */
  private isTargetInput(target: HTMLElement): boolean {
    if (!target) return false;

    // Check by ID
    if (target.id && this.config.targetInputs.some(id => target.id.includes(id))) {
      return true;
    }

    // Check by class names
    const className = target.className || '';
    if (className.includes('governance') || 
        className.includes('chatfork') || 
        className.includes('node-input') ||
        className.includes('mindsheet')) {
      return true;
    }

    // Check by data attributes
    const dataRole = target.getAttribute('data-role');
    if (dataRole && (dataRole.includes('input') || dataRole.includes('editor'))) {
      return true;
    }

    // Check if it's a textarea or input in a relevant container
    if (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT') {
      const container = target.closest('[class*="governance"], [class*="chatfork"], [class*="node"], [class*="mindsheet"]');
      if (container) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if there's active content worth snapshotting
   */
  private hasActiveContent(): boolean {
    try {
      // For now, always return true to allow snapshots
      // This can be made more sophisticated later
      console.log('AutoSnapshotService: Checking active content - allowing snapshot');
      return true;

      // TODO: Implement more sophisticated content checking
      // const mindBookStore = useMindBookStore.getState();
      // const chatStore = useChatStore.getState();
      // return mindBookStore.sheets.length > 0 || chatStore.messages.length > 0;
    } catch (error) {
      console.warn('AutoSnapshotService: Error checking active content:', error);
      // Even if there's an error, allow snapshots for debugging
      return true;
    }
  }

  /**
   * Generate and save a snapshot
   */
  private async generateSnapshot(triggerElement: HTMLElement): Promise<void> {
    this.isProcessing = true;
    this.lastSnapshotTime = Date.now();

    try {
      console.log('🔄 AutoSnapshotService: Generating snapshot...');

      // Get mindbook name
      const mindBookStore = useMindBookStore.getState();
      const mindbookName = mindBookStore.name || 'untitled';

      // Collect snapshot data
      const snapshotData = await MemorySnapshotService.collectMBCPSnapshot();

      // Add trigger information to metadata
      const enhancedSnapshot = {
        ...snapshotData,
        trigger: {
          element: triggerElement.tagName,
          id: triggerElement.id,
          className: triggerElement.className,
          timestamp: new Date().toISOString(),
          type: 'enter_key'
        }
      };

      // Save to backend
      const result = await MemoryAPI.saveSnapshot({
        mindbook_name: mindbookName,
        snapshot_data: enhancedSnapshot
      });

      console.log(`📸 AutoSnapshotService: Snapshot saved successfully: ${result.filename}`);

      // Optionally show a brief notification
      this.showSnapshotNotification(result.filename);

    } catch (error) {
      console.error('AutoSnapshotService: Error generating snapshot:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Show a brief notification that a snapshot was created
   */
  private showSnapshotNotification(filename: string): void {
    // Create a temporary notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10000;
      opacity: 0.9;
      transition: opacity 0.3s ease;
    `;
    notification.textContent = `📸 Snapshot saved: ${filename}`;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  /**
   * Enable or disable auto-snapshots
   */
  setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    console.log(`AutoSnapshotService: ${enabled ? 'Enabled' : 'Disabled'}`);
  }

  /**
   * Check if auto-snapshots are enabled
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AutoSnapshotConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('AutoSnapshotService: Configuration updated:', this.config);
  }

  /**
   * Get current configuration
   */
  getConfig(): AutoSnapshotConfig {
    return { ...this.config };
  }

  /**
   * Manually trigger a snapshot
   */
  async triggerSnapshot(reason: string = 'manual'): Promise<void> {
    if (this.isProcessing) {
      console.log('AutoSnapshotService: Already processing, cannot trigger manual snapshot');
      return;
    }

    try {
      console.log(`🔄 AutoSnapshotService: Manual snapshot triggered (${reason})`);

      const mindBookStore = useMindBookStore.getState();
      const mindbookName = mindBookStore.name || 'untitled';

      const snapshotData = await MemorySnapshotService.collectMBCPSnapshot();

      const enhancedSnapshot = {
        ...snapshotData,
        trigger: {
          type: 'manual',
          reason,
          timestamp: new Date().toISOString()
        }
      };

      const result = await MemoryAPI.saveSnapshot({
        mindbook_name: mindbookName,
        snapshot_data: enhancedSnapshot
      });

      console.log(`📸 AutoSnapshotService: Manual snapshot saved: ${result.filename}`);
      this.showSnapshotNotification(result.filename);

    } catch (error) {
      console.error('AutoSnapshotService: Error in manual snapshot:', error);
      throw error;
    }
  }

  /**
   * Get snapshot statistics
   */
  async getSnapshotStats(): Promise<any> {
    try {
      const stats = await MemoryAPI.getStorageStats();
      return {
        ...stats,
        autoSnapshotConfig: this.config,
        lastSnapshotTime: this.lastSnapshotTime,
        isProcessing: this.isProcessing
      };
    } catch (error) {
      console.error('AutoSnapshotService: Error getting stats:', error);
      return {
        error: error.message,
        autoSnapshotConfig: this.config,
        lastSnapshotTime: this.lastSnapshotTime,
        isProcessing: this.isProcessing
      };
    }
  }
}

// Initialize the service when the module loads
const autoSnapshotService = AutoSnapshotService.getInstance();

// Add to global scope for development
(window as any).autoSnapshot = autoSnapshotService;
(window as any).triggerSnapshot = (reason?: string) => autoSnapshotService.triggerSnapshot(reason);
(window as any).getSnapshotStats = () => autoSnapshotService.getSnapshotStats();

console.log('🔄 AutoSnapshotService: Module loaded and service initialized');
console.log('🔄 AutoSnapshotService: Config:', autoSnapshotService.getConfig());
console.log('🔄 AutoSnapshotService: Global functions available:', {
  autoSnapshot: !!window.autoSnapshot,
  triggerSnapshot: !!window.triggerSnapshot,
  getSnapshotStats: !!window.getSnapshotStats
});

// Test the service after a short delay to ensure DOM is ready
setTimeout(() => {
  console.log('🔄 AutoSnapshotService: Testing service after DOM ready...');
  const governanceInput = document.getElementById('governance-input');
  console.log('🔄 AutoSnapshotService: Governance input found:', !!governanceInput);
  if (governanceInput) {
    console.log('🔄 AutoSnapshotService: Governance input details:', {
      tagName: governanceInput.tagName,
      id: governanceInput.id,
      className: governanceInput.className
    });
  }
}, 2000);

export default autoSnapshotService;
