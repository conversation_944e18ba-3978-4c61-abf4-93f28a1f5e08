/**
 * BlockchainSnapshotService.ts
 * 
 * Implements blockchain-like snapshot chaining for memory continuity.
 * Each snapshot references the previous snapshot, creating an immutable chain.
 * Implements Phase 1.1.6: Event-Based Memory System concepts.
 */

import { MemorySnapshotService, MBCPMemorySnapshot } from '../core/services/MemorySnapshotService';

export interface BlockchainSnapshot extends MBCPMemorySnapshot {
  previousHash: string | null;
  hash: string;
  blockNumber: number;
  deltaChanges?: any; // Only changes since previous snapshot
  validationSignature?: string;
}

export interface SnapshotChain {
  genesis: BlockchainSnapshot;
  latest: BlockchainSnapshot;
  length: number;
  totalSize: number;
  integrity: boolean;
}

export class BlockchainSnapshotService {
  private static readonly CHAIN_STORAGE_KEY = 'mindback_snapshot_chain';
  private static readonly GENESIS_HASH = '0000000000000000000000000000000000000000000000000000000000000000';

  /**
   * Create a new blockchain snapshot linked to the previous one
   */
  static async createBlockchainSnapshot(
    includeFullData: boolean = false
  ): Promise<BlockchainSnapshot> {
    console.log('🔗 Creating blockchain snapshot...');

    // Get the base MBCP snapshot
    const baseSnapshot = await MemorySnapshotService.collectMBCPSnapshot();
    
    // Get the previous snapshot from the chain
    const previousSnapshot = this.getLatestSnapshot();
    
    // Calculate block number
    const blockNumber = previousSnapshot ? previousSnapshot.blockNumber + 1 : 0;
    
    // Calculate delta changes if we have a previous snapshot
    let deltaChanges: any = null;
    if (previousSnapshot && !includeFullData) {
      deltaChanges = this.calculateDelta(previousSnapshot, baseSnapshot);
    }

    // Create the blockchain snapshot
    const blockchainSnapshot: BlockchainSnapshot = {
      ...baseSnapshot,
      id: `blockchain_snapshot_${blockNumber}_${Date.now()}`,
      previousHash: previousSnapshot ? previousSnapshot.hash : null,
      hash: '', // Will be calculated
      blockNumber,
      deltaChanges: includeFullData ? null : deltaChanges
    };

    // Calculate hash for this snapshot
    blockchainSnapshot.hash = await this.calculateHash(blockchainSnapshot);
    
    // Add validation signature
    blockchainSnapshot.validationSignature = await this.generateValidationSignature(blockchainSnapshot);

    // Store in the chain
    this.addToChain(blockchainSnapshot);

    console.log(`🔗 Blockchain snapshot created: Block #${blockNumber}, Hash: ${blockchainSnapshot.hash.substring(0, 8)}...`);
    
    return blockchainSnapshot;
  }

  /**
   * Calculate hash for a snapshot (simplified SHA-256-like)
   */
  private static async calculateHash(snapshot: Omit<BlockchainSnapshot, 'hash'>): Promise<string> {
    // Create a deterministic string representation
    const dataString = JSON.stringify({
      id: snapshot.id,
      timestamp: snapshot.timestamp,
      previousHash: snapshot.previousHash,
      blockNumber: snapshot.blockNumber,
      mindBook: snapshot.mindBook,
      conversationThreads: snapshot.conversationThreads,
      memoryEntries: snapshot.memoryEntries,
      metadata: snapshot.metadata
    });

    // Simple hash calculation (in production, use crypto.subtle.digest)
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      const char = dataString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    // Convert to hex and pad
    const hexHash = Math.abs(hash).toString(16).padStart(8, '0');
    return `${hexHash}${Date.now().toString(16)}${Math.random().toString(16).substring(2, 10)}`;
  }

  /**
   * Generate validation signature for integrity checking
   */
  private static async generateValidationSignature(snapshot: BlockchainSnapshot): Promise<string> {
    const signatureData = `${snapshot.hash}:${snapshot.timestamp}:${snapshot.blockNumber}`;
    
    // Simple signature (in production, use proper cryptographic signing)
    let signature = 0;
    for (let i = 0; i < signatureData.length; i++) {
      signature = ((signature << 3) - signature) + signatureData.charCodeAt(i);
      signature = signature & signature;
    }
    
    return `sig_${Math.abs(signature).toString(16)}`;
  }

  /**
   * Calculate delta changes between two snapshots
   */
  private static calculateDelta(previous: MBCPMemorySnapshot, current: MBCPMemorySnapshot): any {
    const delta: any = {
      timestamp: current.timestamp,
      changes: {}
    };

    // Compare conversation threads
    const prevThreads = Object.keys(previous.conversationThreads || {});
    const currThreads = Object.keys(current.conversationThreads || {});
    
    if (prevThreads.length !== currThreads.length) {
      delta.changes.conversationThreads = {
        added: currThreads.filter(id => !prevThreads.includes(id)),
        removed: prevThreads.filter(id => !currThreads.includes(id))
      };
    }

    // Compare memory entries
    const prevEntries = Object.keys(previous.memoryEntries || {});
    const currEntries = Object.keys(current.memoryEntries || {});
    
    if (prevEntries.length !== currEntries.length) {
      delta.changes.memoryEntries = {
        added: currEntries.filter(id => !prevEntries.includes(id)),
        removed: prevEntries.filter(id => !currEntries.includes(id))
      };
    }

    // Compare sheets
    const prevSheets = previous.mindBook?.sheets?.length || 0;
    const currSheets = current.mindBook?.sheets?.length || 0;
    
    if (prevSheets !== currSheets) {
      delta.changes.sheets = {
        previous: prevSheets,
        current: currSheets,
        delta: currSheets - prevSheets
      };
    }

    // Compare active sheet
    if (previous.mindBook?.activeSheetId !== current.mindBook?.activeSheetId) {
      delta.changes.activeSheet = {
        previous: previous.mindBook?.activeSheetId,
        current: current.mindBook?.activeSheetId
      };
    }

    return delta;
  }

  /**
   * Add snapshot to the blockchain chain
   */
  private static addToChain(snapshot: BlockchainSnapshot): void {
    try {
      const chain = this.getChain();
      chain.push(snapshot);
      
      // Store updated chain
      localStorage.setItem(this.CHAIN_STORAGE_KEY, JSON.stringify(chain));
      
      // Also store individual snapshot for quick access
      localStorage.setItem(`snapshot_${snapshot.blockNumber}`, JSON.stringify(snapshot));
      
      console.log(`🔗 Added snapshot to chain: Block #${snapshot.blockNumber}`);
    } catch (error) {
      console.error('Failed to add snapshot to chain:', error);
    }
  }

  /**
   * Get the complete snapshot chain
   */
  static getChain(): BlockchainSnapshot[] {
    try {
      const chainData = localStorage.getItem(this.CHAIN_STORAGE_KEY);
      return chainData ? JSON.parse(chainData) : [];
    } catch (error) {
      console.error('Failed to load snapshot chain:', error);
      return [];
    }
  }

  /**
   * Get the latest snapshot in the chain
   */
  static getLatestSnapshot(): BlockchainSnapshot | null {
    const chain = this.getChain();
    return chain.length > 0 ? chain[chain.length - 1] : null;
  }

  /**
   * Get snapshot by block number
   */
  static getSnapshotByBlock(blockNumber: number): BlockchainSnapshot | null {
    try {
      const snapshotData = localStorage.getItem(`snapshot_${blockNumber}`);
      return snapshotData ? JSON.parse(snapshotData) : null;
    } catch (error) {
      console.error(`Failed to load snapshot block ${blockNumber}:`, error);
      return null;
    }
  }

  /**
   * Validate the integrity of the entire chain
   */
  static async validateChain(): Promise<{ valid: boolean; errors: string[] }> {
    const chain = this.getChain();
    const errors: string[] = [];

    if (chain.length === 0) {
      return { valid: true, errors: [] };
    }

    // Validate each block
    for (let i = 0; i < chain.length; i++) {
      const snapshot = chain[i];
      
      // Check block number sequence
      if (snapshot.blockNumber !== i) {
        errors.push(`Block ${i}: Invalid block number ${snapshot.blockNumber}`);
      }
      
      // Check previous hash linkage
      if (i === 0) {
        if (snapshot.previousHash !== null) {
          errors.push(`Genesis block should have null previousHash`);
        }
      } else {
        const previousSnapshot = chain[i - 1];
        if (snapshot.previousHash !== previousSnapshot.hash) {
          errors.push(`Block ${i}: Invalid previous hash linkage`);
        }
      }
      
      // Validate hash
      const calculatedHash = await this.calculateHash({
        ...snapshot,
        hash: undefined as any
      });
      
      if (calculatedHash !== snapshot.hash) {
        errors.push(`Block ${i}: Hash validation failed`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get chain statistics
   */
  static getChainInfo(): SnapshotChain | null {
    const chain = this.getChain();
    
    if (chain.length === 0) {
      return null;
    }

    const totalSize = chain.reduce((sum, snapshot) => {
      return sum + JSON.stringify(snapshot).length;
    }, 0);

    return {
      genesis: chain[0],
      latest: chain[chain.length - 1],
      length: chain.length,
      totalSize,
      integrity: true // Would need to run validateChain() for real check
    };
  }

  /**
   * Reconstruct full state from chain (blockchain replay)
   */
  static reconstructStateFromChain(upToBlock?: number): MBCPMemorySnapshot | null {
    const chain = this.getChain();
    
    if (chain.length === 0) {
      return null;
    }

    const targetBlock = upToBlock !== undefined ? Math.min(upToBlock, chain.length - 1) : chain.length - 1;
    
    // Start with genesis block
    let reconstructedState = { ...chain[0] };
    
    // Apply deltas from subsequent blocks
    for (let i = 1; i <= targetBlock; i++) {
      const snapshot = chain[i];
      
      if (snapshot.deltaChanges) {
        // Apply delta changes to reconstruct state
        this.applyDelta(reconstructedState, snapshot.deltaChanges);
      } else {
        // Full snapshot, replace state
        reconstructedState = { ...snapshot };
      }
    }

    return reconstructedState;
  }

  /**
   * Apply delta changes to a state
   */
  private static applyDelta(state: any, delta: any): void {
    if (delta.changes) {
      // Apply conversation thread changes
      if (delta.changes.conversationThreads) {
        // Add new threads, remove deleted ones
        // Implementation would depend on specific delta format
      }
      
      // Apply memory entry changes
      if (delta.changes.memoryEntries) {
        // Add new entries, remove deleted ones
      }
      
      // Apply sheet changes
      if (delta.changes.activeSheet) {
        state.mindBook.activeSheetId = delta.changes.activeSheet.current;
      }
    }
  }

  /**
   * Clear the entire chain (use with caution)
   */
  static clearChain(): void {
    localStorage.removeItem(this.CHAIN_STORAGE_KEY);
    
    // Remove individual snapshots
    const chain = this.getChain();
    chain.forEach(snapshot => {
      localStorage.removeItem(`snapshot_${snapshot.blockNumber}`);
    });
    
    console.log('🔗 Blockchain snapshot chain cleared');
  }
}

// Add to global scope for development
(window as any).createBlockchainSnapshot = () => BlockchainSnapshotService.createBlockchainSnapshot();
(window as any).getSnapshotChain = () => BlockchainSnapshotService.getChainInfo();
(window as any).validateSnapshotChain = () => BlockchainSnapshotService.validateChain();
